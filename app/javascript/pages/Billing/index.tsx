import {
  <PERSON><PERSON>,
  <PERSON>,
  But<PERSON>,
  Card,
  Divider,
  Grid,
  List,
  RadioButton,
  Text,
  TextField,
} from '@shopify/polaris';
import { Formik } from 'formik';
import startCase from 'lodash/startCase';
import * as React from 'react';
import { match } from 'ts-pattern';
import * as Yup from 'yup';

import { CurrentPage } from '@/components/CurrentPage';
import { formatNumber, formatNumberWithDecimal } from '@/components/util';
import { useCurrentShop } from '@/hooks/useCurrentShop';
import * as routes from '@/routes';
import type { Package } from '@/types';

import { StripeModal } from './StripeModal';

interface Plan {
  disabled: boolean;
  name: string;
  planKey: Package;
  priceList: string;
  subtitle: string;
  terms?: string;
  url: string;
}

interface CreditFormValues {
  topup: number;
}

export function Billing() {
  const shop = useCurrentShop().currentShop;

  // pro and basic for old users
  const oldUserPlans = React.useMemo<Array<Plan>>(
    () => [
      {
        name: 'Unlimited Fulfillment',
        subtitle: 'per month',
        planKey: 'unlimited',
        priceList: '$24',
        disabled: shop.package === 'unlimited',
        url: routes.getSubcribeToPlan({ plan: 'unlimited' }),
      },
      {
        name: 'Pro',
        subtitle: 'per month',
        planKey: 'pro',
        priceList: '$10',
        terms: 'max 3 sources',
        disabled: shop.package === 'pro',
        url: routes.getSubcribeToPlan({ plan: 'pro' }),
      },
      {
        name: 'Basic',
        subtitle: 'per month',
        planKey: 'basic',
        priceList: '$5',
        terms: 'max 1 source',
        disabled: shop.package === 'basic',
        url: routes.getSubcribeToPlan({ plan: 'basic' }),
      },
      {
        name: 'Free',
        subtitle: '',
        planKey: 'trial',
        priceList: '$0',
        disabled: shop.package === 'trial',
        url: routes.getSubcribeToPlan({ plan: 'trial' }),
      },
    ],
    [shop]
  );

  const newUserPlans = React.useMemo(
    () =>
      oldUserPlans.filter(
        ({ planKey }) => planKey === 'trial' || planKey === 'unlimited'
      ),
    [oldUserPlans]
  );

  const legacyPlan = React.useMemo(
    () =>
      oldUserPlans.find(
        ({ planKey }) =>
          ['pro', 'basic'].includes(planKey) && planKey === shop.package
      ),
    [oldUserPlans, shop.package]
  );

  const currentPlan = React.useMemo(
    () => oldUserPlans.find(({ planKey }) => planKey === shop.package),
    [oldUserPlans, shop.package]
  );

  const creditTiers = [
    {
      id: '1',
      pricePerFulfillment: 0.0039,
      minRangeValue: 10000,
      maxRangeValue: 20000,
    },
    {
      id: '2',
      pricePerFulfillment: 0.01,
      minRangeValue: 2000,
      maxRangeValue: 9999,
    },
    {
      id: '3',
      pricePerFulfillment: 0.01,
      minRangeValue: 500,
      maxRangeValue: 1999,
    },
  ];

  function getPrice(topup: number) {
    const matchingTier = creditTiers.find(
      (c) => topup >= c.minRangeValue && topup <= c.maxRangeValue
    );
    const [lowestTier] = creditTiers;

    let cost = 0;

    if (matchingTier) {
      cost = topup * matchingTier.pricePerFulfillment;
    } else if (topup < lowestTier.minRangeValue) {
      cost = topup * lowestTier.pricePerFulfillment;
    }

    return cost;
  }

  console.log('currentPlan', currentPlan);
  console.log('newUserPlans', newUserPlans);

  return (
    <CurrentPage
      titleMetadata="Billing"
      backAction={{
        content: 'Back to dashboard',
        url: routes.dashboard,
      }}
      secondaryActions={[
        {
          content: 'Fulfillment log',
          url: routes.fulfillments,
        },
        {
          content: 'Preferences',
          url: routes.editPreferences,
        },
      ]}
    >
      {!shop.connected && (
        <div style={{ marginBottom: '16px' }}>
          <Banner tone="warning">
            Please fill in the api credentials in order to integrate with
            Bigcommerce.
          </Banner>
        </div>
      )}
      <div style={{ marginBottom: '16px' }}>
        <Banner title="Need enterprise solutions?">
          <List type="bullet">
            <List.Item>Custom API integration</List.Item>
            <List.Item>Critical data synced every 10 mins</List.Item>
            <List.Item>
              Full-stack ERP sync tailored for enterprise merchants"
            </List.Item>
          </List>
          <div style={{ marginTop: '16px' }}>
            <Button>Contact support</Button>
          </div>
        </Banner>
      </div>
      <Card>
        <div style={{ marginBottom: '20px', display: 'inline-block' }}>
          <Text variant="headingMd" as="h2">
            Monthly subscription -{' '}
            {startCase(shop.package === 'trial' ? 'Free' : shop.package)}{' '}
            (monthly) {legacyPlan ? '(Legacy)' : ''}
          </Text>
          <div style={{ marginTop: '4px' }}>
            <Text variant="headingMd" as="h2">
              {currentPlan?.priceList}{' '}
              <Text variant="bodyMd" as="span">
                /mo
              </Text>
            </Text>
          </div>
        </div>
        {legacyPlan ? (
          <Banner title="You're currently on an old pricing plan. Upgrade now to access the latest benefits with the plans below" />
        ) : (
          <></>
        )}
      </Card>

      <div style={{ marginTop: '16px' }}>
        <Grid>
          {newUserPlans.map((plan) => (
            <Grid.Cell
              key={plan.planKey}
              columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}
            >
              <Card>
                <Text variant="headingMd" as="h2">
                  {plan.name}{' '}
                  {plan.planKey === 'unlimited' ? (
                    <Badge tone="success">Best value</Badge>
                  ) : (
                    <></>
                  )}
                </Text>
                <div style={{ marginTop: '16px' }}>
                  <Button
                    disabled={currentPlan?.planKey === plan.planKey}
                    variant="primary"
                    onClick={() => {
                      window.location.href = plan.url;
                    }}
                  >
                    {currentPlan?.planKey === plan.planKey
                      ? 'Current Plan'
                      : `Choose ${plan.planKey === 'trial' ? 'free' : plan.planKey}`}
                  </Button>
                  <div style={{ marginTop: '8px', display: 'flex' }}>
                    <Text variant="headingMd" as="h2">
                      {plan.priceList}{' '}
                      <Text variant="bodyMd" as="span">
                        /mo
                      </Text>
                    </Text>{' '}
                    {plan.planKey === 'unlimited' ? (
                      <div
                        style={{
                          textDecoration: 'line-through',
                          marginLeft: '8px',
                        }}
                      >
                        <Text variant="headingMd" as="h2">
                          $39
                        </Text>
                      </div>
                    ) : (
                      <></>
                    )}
                  </div>
                  <div style={{ margin: '16px 0px' }}>
                    <Divider />
                  </div>
                  <List type="bullet">
                    {match(plan.planKey)
                      .with('unlimited', () => (
                        <>
                          <List.Item>
                            Unlimited fulfillment every month
                          </List.Item>
                          <List.Item>Partial fulfillment</List.Item>
                          <List.Item>Sync tracking info to Paypal</List.Item>
                          <List.Item>All features</List.Item>
                        </>
                      ))
                      .otherwise(() => (
                        <>
                          <List.Item>First Free 3,000 orders</List.Item>
                          <List.Item>
                            After first 3,000 order, $0.01 per additional
                            fulfillment
                          </List.Item>
                          <List.Item>Sync tracking info to Paypal</List.Item>
                          <List.Item>All features</List.Item>
                        </>
                      ))}
                  </List>
                </div>
              </Card>
            </Grid.Cell>
          ))}
        </Grid>
      </div>

      <Grid>
        <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
          <div style={{ marginTop: '24px' }}>
            <Text variant="headingMd" as="h2">
              Pay as you go (credits)
            </Text>

            <div style={{ marginTop: '16px' }}>
              <Text variant="headingMd" as="h2">
                Credits
              </Text>

              <Text variant="bodyMd" as="p">
                Boost your plan with extra fulfillment
              </Text>
            </div>

            <div style={{ marginTop: '16px' }}>
              <Formik
                enableReinitialize
                initialValues={{ topup: 10000 } satisfies CreditFormValues}
                validationSchema={Yup.object({
                  credit: Yup.number()
                    .min(200, 'Fulfillment must be greater than 199')
                    .max(20000, 'Fulfillment must be smaller than  20001'),
                })}
                onSubmit={(values) => {
                  window.location.href = routes.getCreditSystemRoute(
                    values.topup
                  );
                }}
              >
                {({
                  errors,
                  handleSubmit,
                  setFieldValue,
                  values,
                  isSubmitting,
                }) => (
                  <form onSubmit={handleSubmit}>
                    <TextField
                      label=""
                      autoComplete="off"
                      type="number"
                      value={values.topup.toString()}
                      error={errors.topup}
                      onChange={(topup) => {
                        setFieldValue(
                          'topup' satisfies keyof CreditFormValues,
                          Number(topup)
                        );
                      }}
                      connectedRight={
                        shop.provider === 'Shopify' ? (
                          <Button
                            variant="primary"
                            submit
                            loading={isSubmitting}
                            disabled={!shop.useCredit}
                          >
                            Top Up - $
                            {formatNumberWithDecimal(getPrice(values.topup))}
                          </Button>
                        ) : (
                          <StripeModal
                            credit={values.topup}
                            price={formatNumberWithDecimal(
                              getPrice(values.topup)
                            )}
                          />
                        )
                      }
                    />

                    <div style={{ marginTop: '16px' }}>
                      {creditTiers.map((creditTier) => (
                        <div
                          style={{ marginBottom: '8px' }}
                          key={creditTier.id}
                        >
                          <Card>
                            <RadioButton
                              label={`${formatNumber(creditTier.minRangeValue)} | $${formatNumberWithDecimal(
                                creditTier.minRangeValue *
                                  creditTier.pricePerFulfillment
                              )}`}
                              helpText={`${creditTier.pricePerFulfillment}  per fulfillment`}
                              checked={
                                creditTier.minRangeValue === values.topup
                              }
                              id={creditTier.minRangeValue.toString()}
                              onChange={(_, newValue) =>
                                setFieldValue(
                                  'topup' satisfies keyof CreditFormValues,
                                  Number(newValue)
                                )
                              }
                            />
                          </Card>
                        </div>
                      ))}
                    </div>
                  </form>
                )}
              </Formik>
              <div style={{ marginTop: '24px' }}>
                <Banner title="Tips: More than 7,500 orders? Save $10/mon with Unlimited plan!" />
              </div>
            </div>
          </div>
        </Grid.Cell>
      </Grid>
    </CurrentPage>
  );
}
