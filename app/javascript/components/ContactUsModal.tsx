import { Modal, type ModalProps } from '@shopify/polaris';

interface ContactUsModalProp extends Pick<ModalProps, 'open' | 'onClose'> {}

export function ContactUsModal({ open, onClose }: ContactUsModalProp) {
  return (
    <Modal open={open} onClose={onClose} title="Contact Us">
      <Modal.Section>
        Email us at{' '}
        <a href="mailto:<EMAIL>"><EMAIL></a>
      </Modal.Section>
    </Modal>
  );
}
