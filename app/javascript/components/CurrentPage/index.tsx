import {
  Footer<PERSON>elp,
  InlineStack,
  Page,
  Text,
  type PageProps,
} from '@shopify/polaris';
import * as React from 'react';

import { Link } from '@/components/Link';
import { useCurrentShop } from '@/hooks/useCurrentShop';
import * as routes from '@/routes';

import { ContactUsModal } from '../ContactUsModal';
import classes from './index.module.css';

interface CurrentPageProps
  extends Pick<
    PageProps,
    | 'children'
    | 'titleMetadata'
    | 'primaryAction'
    | 'actionGroups'
    | 'backAction'
    | 'secondaryActions'
  > {}

export function CurrentPage({
  children,
  titleMetadata,
  primaryAction,
  actionGroups,
  backAction,
  secondaryActions,
}: CurrentPageProps) {
  const { currentShop } = useCurrentShop();
  const [contactUsModalOpen, setContactUsModalOpen] = React.useState(false);

  return (
    <div className={classes.root}>
      <div
        style={{
          margin: '24px auto',
          padding: '0px 24px',
          maxWidth: '998px',
          width: '100%',
        }}
      >
        <InlineStack wrap={false}>
          <Link url={routes.dashboard}>
            <img
              src="/FSicon.svg"
              style={{
                width: '40px',
                height: 'auto',
                marginLeft: '8px',
              }}
            />
          </Link>
          <div>
            <p style={{ display: 'grid', marginLeft: '8px' }}>
              <strong>syncX: Fulfill Tracking</strong>
              <span style={{ color: '#616161' }}>
                {currentShop.shopifyDomain}
              </span>
            </p>
          </div>
        </InlineStack>
      </div>
      <Page
        titleMetadata={
          <Text as="h1" variant="headingLg">
            {titleMetadata}
          </Text>
        }
        backAction={backAction}
        primaryAction={primaryAction}
        secondaryActions={secondaryActions}
        actionGroups={actionGroups}
      >
        <ContactUsModal
          open={contactUsModalOpen}
          onClose={() => setContactUsModalOpen(false)}
        />
        {children}
        <FooterHelp>
          Click more about syncX: Fufill Tracking
          <Link
            url="https://fulfilltracking.freshdesk.com/support/home"
            external
          >
            {' '}
            Help center
          </Link>
        </FooterHelp>
      </Page>
    </div>
  );
}
